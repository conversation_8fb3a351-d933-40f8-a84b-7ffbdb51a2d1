-- =====================================================
-- TEST SCRIPT FOR EXTENDED SCHEMA (Phase 1)
-- Tests new tables: students, applications, users, application_documents
-- =====================================================

-- Test 1: Create test users
INSERT INTO users (username, email, password_hash, role) VALUES
('admin_test', '<EMAIL>', '$2b$10$test_hash_admin', 'admin'),
('student_test', '<EMAIL>', '$2b$10$test_hash_student', 'student'),
('staff_test', '<EMAIL>', '$2b$10$test_hash_staff', 'staff');

-- Test 2: Create test students (requires existing programs and campuses)
INSERT INTO students (
    student_code, full_name, email, phone, date_of_birth, gender, 
    address, program_id, campus_id, admission_year, status
) VALUES (
    'HN25001', 
    'Ng<PERSON>ễn Văn Test', 
    '<EMAIL>', 
    '0123456789',
    '2005-01-15',
    'male',
    'Hà Nội, Việt Nam',
    (SELECT id FROM programs WHERE code = 'SE' LIMIT 1),
    (SELECT id FROM campuses WHERE code = 'HN' LIMIT 1),
    2025,
    'active'
);

-- Test 3: Create test applications
INSERT INTO applications (
    application_code, student_name, email, phone, 
    program_id, campus_id, admission_method_id, status
) VALUES (
    generate_application_code(),
    'Trần Thị Test Application',
    '<EMAIL>',
    '0987654321',
    (SELECT id FROM programs WHERE code = 'AI' LIMIT 1),
    (SELECT id FROM campuses WHERE code = 'HCM' LIMIT 1),
    (SELECT id FROM admission_methods WHERE method_code = 'THPT' LIMIT 1),
    'pending'
);

-- Test 4: Create test documents for application
INSERT INTO application_documents (
    application_id, document_type, file_name, file_path, file_size, mime_type
) VALUES (
    (SELECT id FROM applications WHERE student_name = 'Trần Thị Test Application' LIMIT 1),
    'transcript',
    'bang_diem_thpt.pdf',
    '/uploads/documents/transcript_001.pdf',
    1024000,
    'application/pdf'
);

-- Test 5: Test views
SELECT 'Testing v_applications_complete view:' as test_name;
SELECT 
    application_code, student_name, program_name, campus_name, 
    admission_method, status, document_count
FROM v_applications_complete 
LIMIT 5;

SELECT 'Testing v_students_complete view:' as test_name;
SELECT 
    student_code, full_name, program_name, campus_name, status
FROM v_students_complete 
LIMIT 5;

-- Test 6: Test functions
SELECT 'Testing generate_application_code function:' as test_name;
SELECT generate_application_code() as new_app_code;

SELECT 'Testing generate_student_code function:' as test_name;
SELECT generate_student_code('HN', 2025) as new_student_code;

-- Test 7: Test data summary with new tables
SELECT 'Testing updated v_data_summary view:' as test_name;
SELECT * FROM v_data_summary ORDER BY table_name;

-- Test 8: Test constraints
SELECT 'Testing constraints:' as test_name;

-- This should fail (invalid gender)
DO $$
BEGIN
    INSERT INTO students (student_code, full_name, email, gender) 
    VALUES ('TEST001', 'Test Invalid Gender', '<EMAIL>', 'invalid');
    RAISE NOTICE 'ERROR: Gender constraint should have failed!';
EXCEPTION
    WHEN check_violation THEN
        RAISE NOTICE 'SUCCESS: Gender constraint working correctly';
END $$;

-- This should fail (invalid status)
DO $$
BEGIN
    INSERT INTO applications (application_code, student_name, email, program_id, campus_id, status) 
    VALUES ('TEST001', 'Test Invalid Status', '<EMAIL>', 
            (SELECT id FROM programs LIMIT 1), 
            (SELECT id FROM campuses LIMIT 1), 
            'invalid_status');
    RAISE NOTICE 'ERROR: Status constraint should have failed!';
EXCEPTION
    WHEN check_violation THEN
        RAISE NOTICE 'SUCCESS: Status constraint working correctly';
END $$;

-- Test 9: Test indexes (check if they exist)
SELECT 'Testing indexes:' as test_name;
SELECT 
    schemaname, tablename, indexname, indexdef
FROM pg_indexes 
WHERE tablename IN ('students', 'applications', 'users', 'application_documents')
ORDER BY tablename, indexname;

-- Test 10: Performance test with search
SELECT 'Testing search performance:' as test_name;
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM v_applications_complete 
WHERE status = 'pending' 
ORDER BY submitted_at DESC 
LIMIT 10;

-- Cleanup test data
DELETE FROM application_documents WHERE file_name LIKE '%test%';
DELETE FROM applications WHERE student_name LIKE '%Test%';
DELETE FROM students WHERE student_code LIKE '%TEST%' OR full_name LIKE '%Test%';
DELETE FROM users WHERE username LIKE '%test%';

SELECT 'Schema extension test completed successfully!' as result;
